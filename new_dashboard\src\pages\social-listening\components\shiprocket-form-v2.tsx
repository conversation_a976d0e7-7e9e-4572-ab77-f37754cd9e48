import React from 'react';
import { connectDisconnectToShipRocket } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/Shiprocket-logo.png';
import { shiprocketIntegrationSteps } from '../utils/constant';
import endPoints from '../apis/agent';
import { IntegrationFormFactory, IntegrationConfig } from './integration-components/integration-form-factory';

const ShipRocketFormV2: React.FC = () => {
  const client_id = LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails)?.client_id;

  const { mutateAsync: validateShipRocketAcc } = useApiMutation<
    { valid: boolean; message: string },
    { email: string; password: string }
  >({
    mutationFn: endPoints.validateShipRocketAcc,
  });

  const { mutateAsync: encryptCreds } = useApiMutation({
    mutationFn: endPoints.encryptCreds,
  });

  const shiprocketConfig: IntegrationConfig = {
    name: 'Shiprocket',
    title: 'Shiprocket Integration',
    description: 'Connect your Shiprocket account to manage shipments and logistics seamlessly',
    logo: image,
    logoAlt: 'Shiprocket Logo',
    steps: shiprocketIntegrationSteps,
    fields: [
      {
        name: 'email',
        type: 'email',
        label: 'Email Address',
        placeholder: 'Enter your Shiprocket email address',
        required: true
      },
      {
        name: 'password',
        type: 'password',
        label: 'Password',
        placeholder: 'Enter your Shiprocket password',
        required: true
      }
    ],
    submitButtonText: 'Connect to Shiprocket',
    loadingText: 'Connecting to Shiprocket...',
    successMessage: 'Shiprocket connection established successfully! Redirecting...',
    redirectPath: '/integrations',
    redirectDelay: 3000
  };

  const handleValidateCredentials = async (formData: Record<string, string>) => {
    return await validateShipRocketAcc({
      email: formData.email,
      password: formData.password,
    });
  };

  const handleEncryptCredentials = async (formData: Record<string, string>) => {
    const encryptedPassword = await encryptCreds({
      password: formData.password,
    });
    
    return {
      ...formData,
      password: encryptedPassword
    };
  };

  const handleSubmit = async (formData: Record<string, string>) => {
    if (!client_id) throw new Error('Client ID not found');

    await connectDisconnectToShipRocket({
      channel_name: 'shiprocket',
      client_id,
      email: formData.email,
      password: formData.password,
      isConnect: true,
    });
  };

  return (
    <IntegrationFormFactory
      config={shiprocketConfig}
      onSubmit={handleSubmit}
      validateCredentials={handleValidateCredentials}
      encryptCredentials={handleEncryptCredentials}
    />
  );
};

export default ShipRocketFormV2;
