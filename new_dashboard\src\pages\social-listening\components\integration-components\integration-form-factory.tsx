import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ModernIntegrationWrapper } from './modern-integration-wrapper';
import { ModernSellerPanel, ModernFormField } from './modern-form-components';
import { Keys, LocalStorageService } from '../../../../utils/local-storage';
import { AuthUser } from '../../../../types/auth';

export interface FormField {
  name: string;
  type: 'text' | 'email' | 'password' | 'url';
  label: string;
  placeholder: string;
  required?: boolean;
}

export interface IntegrationConfig {
  name: string;
  title: string;
  description: string;
  logo: string;
  logoAlt: string;
  steps: string[];
  fields: FormField[];
  submitButtonText?: string;
  loadingText?: string;
  successMessage?: string;
  redirectPath?: string;
  redirectDelay?: number;
}

export interface IntegrationFormFactoryProps {
  config: IntegrationConfig;
  onSubmit: (formData: Record<string, string>) => Promise<void>;
  validateCredentials?: (formData: Record<string, string>) => Promise<{ valid: boolean; message: string }>;
  encryptCredentials?: (formData: Record<string, string>) => Promise<Record<string, string>>;
}

export const IntegrationFormFactory: React.FC<IntegrationFormFactoryProps> = ({
  config,
  onSubmit,
  validateCredentials,
  encryptCredentials
}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [apiResponse, setApiResponse] = useState<{ success: boolean; message: string } | null>(null);
  const [unmounted, setUnmounted] = useState(false);

  const client_id = LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails)?.client_id;

  // Initialize form data based on config fields
  const initialFormData = config.fields.reduce((acc, field) => {
    acc[field.name] = '';
    return acc;
  }, {} as Record<string, string>);

  const [formData, setFormData] = useState(initialFormData);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (unmounted) return;
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));
    },
    [unmounted]
  );

  const handleSubmit = useCallback(
    async (e: React.FormEvent<HTMLFormElement>) => {
      if (!client_id) return;
      e.preventDefault();
      if (unmounted) return;

      try {
        setIsLoading(true);
        setApiResponse({ success: false, message: '' });

        // Validate credentials if validator is provided
        if (validateCredentials) {
          const validationResult = await validateCredentials(formData);
          if (!validationResult.valid) {
            setApiResponse({
              success: false,
              message: validationResult.message
            });
            return;
          }
        }

        // Encrypt credentials if encryptor is provided
        let processedData = formData;
        if (encryptCredentials) {
          processedData = await encryptCredentials(formData);
        }

        // Submit the form
        await onSubmit(processedData);

        // Show success message
        setApiResponse({
          success: true,
          message: config.successMessage || 'Connection established successfully! Redirecting...'
        });

        // Reset form
        setFormData(initialFormData);

        // Redirect after delay
        setTimeout(() => {
          navigate(config.redirectPath || '/integrations');
        }, config.redirectDelay || 3000);

      } catch (error) {
        let errorMessage = `Error connecting to ${config.name}`;
        
        if (
          error &&
          typeof error === 'object' &&
          'response' in error &&
          error.response &&
          typeof error.response === 'object' &&
          'data' in error.response &&
          error.response.data &&
          typeof error.response.data === 'object' &&
          'message' in error.response.data
        ) {
          errorMessage = (error.response.data as { message?: string }).message || errorMessage;
        }

        setApiResponse({
          success: false,
          message: errorMessage
        });
      } finally {
        setIsLoading(false);
      }
    },
    [formData, client_id, unmounted, config, onSubmit, validateCredentials, encryptCredentials, navigate, initialFormData]
  );

  useEffect(() => {
    setUnmounted(false);
    return () => {
      setUnmounted(true);
      setFormData(initialFormData);
      setApiResponse(null);
    };
  }, [initialFormData]);

  // Check if all required fields are filled
  const isFormValid = config.fields
    .filter(field => field.required !== false)
    .every(field => formData[field.name]?.trim());

  return (
    <ModernIntegrationWrapper
      title={config.title}
      description={config.description}
      logo={config.logo}
      logoAlt={config.logoAlt}
      steps={config.steps}
    >
      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">Account Credentials</h3>
          <p className="text-sm text-gray-600">
            Please provide your {config.name} credentials to establish the connection
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            {config.fields.map((field) => (
              <ModernFormField
                key={field.name}
                id={field.name}
                name={field.name}
                type={field.type}
                label={field.label}
                placeholder={field.placeholder}
                value={formData[field.name]}
                onChange={handleChange}
                required={field.required !== false}
                disabled={isLoading}
              />
            ))}
          </div>

          {apiResponse && apiResponse.message && (
            <div className={`p-4 rounded-lg border-l-4 ${
              apiResponse.success 
                ? 'border-l-green-500 bg-green-50 text-green-800' 
                : 'border-l-red-500 bg-red-50 text-red-800'
            }`}>
              <p className="font-medium">{apiResponse.message}</p>
            </div>
          )}

          <button
            type="submit"
            disabled={isLoading || !isFormValid}
            className="w-full h-11 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                {config.loadingText || 'Connecting...'}
              </>
            ) : (
              config.submitButtonText || 'Connect'
            )}
          </button>
        </form>
      </div>
    </ModernIntegrationWrapper>
  );
};
