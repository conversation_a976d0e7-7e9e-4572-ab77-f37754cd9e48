import React, { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, CheckCircle2, Circle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/utils';

interface IntegrationStep {
  title: string;
  description: string;
  isCompleted?: boolean;
}

interface ModernIntegrationWrapperProps {
  title: string;
  description: string;
  logo: string;
  logoAlt: string;
  steps: string[];
  children: ReactNode;
  className?: string;
}

const parseStepContent = (stepText: string, index: number): IntegrationStep => {
  // Extract step title and description from the step text
  const stepNumber = index + 1;
  const title = `Step ${stepNumber}`;
  
  // Clean up the description by removing HTML-like tags and formatting
  const description = stepText
    .replace(/<([^:]+):([^>]+)>/g, '$1') // Remove link formatting
    .replace(/\n/g, ' ') // Replace newlines with spaces
    .trim();
  
  return {
    title,
    description,
    isCompleted: false
  };
};

const IntegrationSteps: React.FC<{ steps: IntegrationStep[] }> = ({ steps }) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <CheckCircle2 className="h-5 w-5 text-green-600" />
        <h3 className="text-lg font-semibold text-gray-900">Integration Steps</h3>
      </div>

      <div className="relative">
        {steps.map((step, index) => (
          <div key={index} className="flex gap-3 group relative">
            {/* Dotted line connector */}
            {index < steps.length - 1 && (
              <div
                className="absolute left-2 top-6 w-0.5 h-8 border-l-2 border-dotted border-green-400"
                style={{ transform: 'translateX(-1px)' }}
              />
            )}

            <div className="flex-shrink-0 mt-1 relative z-10">
              <CheckCircle2 className="h-4 w-4 text-green-600 bg-white" />
            </div>
            <div className="flex-1 min-w-0 pb-6">
              <h4 className="text-sm font-medium text-gray-900 mb-1">
                {step.title}
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed">
                {step.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const IntegrationHeader: React.FC<{
  logo: string;
  logoAlt: string;
  title: string;
  description: string;
}> = ({ logo, logoAlt, title, description }) => {
  return (
    <div className="flex items-center gap-4 mb-6">
      <div className="flex-shrink-0">
        <div className="w-16 h-16 bg-white rounded-xl shadow-sm border border-gray-200 flex items-center justify-center p-2">
          <img 
            src={logo} 
            alt={logoAlt}
            className="max-w-full max-h-full object-contain"
          />
        </div>
      </div>
      <div className="flex-1">
        <h2 className="text-xl font-semibold text-gray-900 mb-1">{title}</h2>
        <p className="text-sm text-gray-600">{description}</p>
        <Badge variant="secondary" className="mt-2">
          Integration Setup
        </Badge>
      </div>
    </div>
  );
};

export const ModernIntegrationWrapper: React.FC<ModernIntegrationWrapperProps> = ({
  title,
  description,
  logo,
  logoAlt,
  steps,
  children,
  className
}) => {
  const navigate = useNavigate();
  
  const parsedSteps = steps.map((step, index) => parseStepContent(step, index));
  
  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      <div className="max-w-7xl mx-auto p-6">
        {/* Back Navigation */}
        <Button
          variant="ghost"
          onClick={handleGoBack}
          className="mb-6 text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Integrations
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Steps */}
          <div className="space-y-6">
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Setup Instructions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <IntegrationSteps steps={parsedSteps} />
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Form */}
          <div className="space-y-6">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <IntegrationHeader
                  logo={logo}
                  logoAlt={logoAlt}
                  title={title}
                  description={description}
                />
                <Separator />
              </CardHeader>
              <CardContent className="pt-6">
                {children}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
