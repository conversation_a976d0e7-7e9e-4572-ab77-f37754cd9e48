import React from 'react';
import { IntegrationFormFactory, IntegrationConfig } from './integration-form-factory';

/**
 * Example Integration Form
 * 
 * This is a template showing how to create a new integration form
 * using the IntegrationFormFactory. Simply copy this file and modify
 * the configuration to match your integration requirements.
 */

const ExampleIntegrationForm: React.FC = () => {
  // Configuration object defining the integration
  const integrationConfig: IntegrationConfig = {
    name: 'Example Service',
    title: 'Example Service Integration',
    description: 'Connect your Example Service account to sync data and automate workflows',
    logo: '/path/to/example-service-logo.png', // Replace with actual logo path
    logoAlt: 'Example Service Logo',
    steps: [
      'Log in to your Example Service account',
      'Navigate to Settings → API Management',
      'Click on "Generate New API Key"',
      'Copy the API Key and Secret Key',
      'Paste the credentials in the form below',
      'Click Connect to establish the integration'
    ],
    fields: [
      {
        name: 'api<PERSON><PERSON>',
        type: 'text',
        label: 'API Key',
        placeholder: 'Enter your API key (e.g., ex_1234567890abcdef)',
        required: true
      },
      {
        name: 'secret<PERSON><PERSON>',
        type: 'password',
        label: 'Secret Key',
        placeholder: 'Enter your secret key',
        required: true
      },
      {
        name: 'serverUrl',
        type: 'url',
        label: 'Server URL',
        placeholder: 'https://api.example-service.com',
        required: false // Optional field
      }
    ],
    submitButtonText: 'Connect to Example Service',
    loadingText: 'Establishing connection...',
    successMessage: 'Example Service connected successfully! Redirecting to integrations page...',
    redirectPath: '/integrations',
    redirectDelay: 3000
  };

  // Optional: Custom credential validation
  const handleValidateCredentials = async (formData: Record<string, string>) => {
    try {
      // Replace with actual API validation call
      const response = await fetch(`${formData.serverUrl || 'https://api.example-service.com'}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${formData.apiKey}`
        },
        body: JSON.stringify({
          secret: formData.secretKey
        })
      });

      const result = await response.json();
      
      return {
        valid: response.ok && result.valid,
        message: result.message || (response.ok ? 'Credentials validated successfully' : 'Invalid credentials')
      };
    } catch (error) {
      return {
        valid: false,
        message: 'Failed to validate credentials. Please check your network connection.'
      };
    }
  };

  // Optional: Custom credential encryption
  const handleEncryptCredentials = async (formData: Record<string, string>) => {
    // Replace with actual encryption logic if needed
    // For example, you might want to encrypt the secret key
    return {
      ...formData,
      secretKey: btoa(formData.secretKey) // Simple base64 encoding (use proper encryption in production)
    };
  };

  // Main submission handler
  const handleSubmit = async (formData: Record<string, string>) => {
    try {
      // Replace with actual integration API call
      const response = await fetch('/api/integrations/example-service/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          apiKey: formData.apiKey,
          secretKey: formData.secretKey,
          serverUrl: formData.serverUrl,
          // Add any other required fields
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to connect to Example Service');
      }

      // Success - the factory will handle the success message and redirect
    } catch (error) {
      // Re-throw the error so the factory can handle it
      throw error;
    }
  };

  return (
    <IntegrationFormFactory
      config={integrationConfig}
      onSubmit={handleSubmit}
      validateCredentials={handleValidateCredentials}
      encryptCredentials={handleEncryptCredentials}
    />
  );
};

export default ExampleIntegrationForm;

/**
 * Usage Instructions:
 * 
 * 1. Copy this file and rename it to match your integration (e.g., shopify-form-v2.tsx)
 * 2. Update the integrationConfig object with your service details
 * 3. Replace the logo path with your service logo
 * 4. Modify the steps array to match your integration process
 * 5. Update the fields array to match your required form fields
 * 6. Implement the handleSubmit function with your integration logic
 * 7. Optionally implement handleValidateCredentials for real-time validation
 * 8. Optionally implement handleEncryptCredentials for sensitive data encryption
 * 9. Import and use the component in your routing configuration
 * 
 * That's it! Your new integration form is ready with modern UI/UX.
 */
