# Modern Integration Components

This directory contains a modular, reusable system for creating integration forms with modern UI/UX using shadcn components.

## Components Overview

### 1. ModernIntegrationWrapper
A layout wrapper that provides a consistent structure for all integration pages.

**Features:**
- Responsive two-column layout
- Left panel for integration steps
- Right panel for form content
- Modern card-based design
- Back navigation
- Integration logo and branding

### 2. ModernFormComponents
Individual form components with modern styling:

- `ModernFormField`: Standard input field with label and validation
- `ModernPasswordField`: Password field with show/hide toggle
- `ModernSellerPanel`: Complete form panel with validation and submission
- `ModernApiResponse`: Styled success/error message display

### 3. IntegrationFormFactory
A factory component that generates complete integration forms from configuration objects.

**Benefits:**
- Reduces code duplication
- Consistent UI/UX across all integrations
- Easy to add new integrations
- Built-in validation and error handling
- Automatic form state management

## Usage Examples

### Basic Integration Form (Manual Approach)

```tsx
import { ModernIntegrationWrapper, ModernSellerPanel } from './integration-components';

const MyIntegrationForm = () => {
  // ... state and handlers

  return (
    <ModernIntegrationWrapper
      title="My Service Integration"
      description="Connect your account to sync data"
      logo={myServiceLogo}
      logoAlt="My Service Logo"
      steps={integrationSteps}
    >
      <ModernSellerPanel
        title="Account Credentials"
        description="Enter your credentials"
        email={email}
        password={password}
        onChange={handleChange}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        apiResponse={apiResponse}
      />
    </ModernIntegrationWrapper>
  );
};
```

### Factory-Based Integration Form (Recommended)

```tsx
import { IntegrationFormFactory, IntegrationConfig } from './integration-components';

const MyIntegrationForm = () => {
  const config: IntegrationConfig = {
    name: 'MyService',
    title: 'My Service Integration',
    description: 'Connect your account to sync data seamlessly',
    logo: myServiceLogo,
    logoAlt: 'My Service Logo',
    steps: [
      'Log in to your account',
      'Go to API settings',
      'Generate API credentials',
      'Enter credentials here'
    ],
    fields: [
      {
        name: 'apiKey',
        type: 'text',
        label: 'API Key',
        placeholder: 'Enter your API key',
        required: true
      },
      {
        name: 'secretKey',
        type: 'password',
        label: 'Secret Key',
        placeholder: 'Enter your secret key',
        required: true
      }
    ],
    submitButtonText: 'Connect to My Service',
    loadingText: 'Connecting...',
    successMessage: 'Connected successfully!',
    redirectPath: '/integrations'
  };

  const handleSubmit = async (formData: Record<string, string>) => {
    // Your integration logic here
    await connectToMyService(formData);
  };

  return (
    <IntegrationFormFactory
      config={config}
      onSubmit={handleSubmit}
    />
  );
};
```

## Configuration Options

### IntegrationConfig Interface

```tsx
interface IntegrationConfig {
  name: string;                    // Service name
  title: string;                   // Display title
  description: string;             // Service description
  logo: string;                    // Logo image path
  logoAlt: string;                 // Logo alt text
  steps: string[];                 // Integration steps
  fields: FormField[];             // Form fields configuration
  submitButtonText?: string;       // Custom submit button text
  loadingText?: string;            // Custom loading text
  successMessage?: string;         // Custom success message
  redirectPath?: string;           // Redirect path after success
  redirectDelay?: number;          // Redirect delay in ms
}
```

### FormField Interface

```tsx
interface FormField {
  name: string;                    // Field name (used as form key)
  type: 'text' | 'email' | 'password' | 'url';  // Input type
  label: string;                   // Field label
  placeholder: string;             // Placeholder text
  required?: boolean;              // Whether field is required (default: true)
}
```

## Advanced Features

### Custom Validation

```tsx
const handleValidateCredentials = async (formData: Record<string, string>) => {
  const response = await validateApiCredentials(formData);
  return {
    valid: response.isValid,
    message: response.message
  };
};

<IntegrationFormFactory
  config={config}
  onSubmit={handleSubmit}
  validateCredentials={handleValidateCredentials}
/>
```

### Credential Encryption

```tsx
const handleEncryptCredentials = async (formData: Record<string, string>) => {
  const encrypted = await encryptSensitiveData(formData);
  return encrypted;
};

<IntegrationFormFactory
  config={config}
  onSubmit={handleSubmit}
  encryptCredentials={handleEncryptCredentials}
/>
```

## Styling and Theming

The components use Tailwind CSS classes and shadcn design tokens for consistent theming:

- **Colors**: Uses semantic color tokens (blue-600, gray-900, etc.)
- **Spacing**: Consistent spacing scale (space-y-6, p-4, etc.)
- **Typography**: Semantic text sizes (text-lg, text-sm, etc.)
- **Borders**: Consistent border radius and colors
- **Shadows**: Subtle shadows for depth

## Migration Guide

To migrate existing integration forms:

1. **Identify reusable patterns** in your current form
2. **Extract configuration** into an IntegrationConfig object
3. **Replace old components** with IntegrationFormFactory
4. **Test the integration** to ensure functionality is preserved
5. **Remove old component files** once migration is complete

## Best Practices

1. **Use the factory approach** for new integrations
2. **Keep step descriptions clear and concise**
3. **Provide meaningful error messages**
4. **Use appropriate input types** for better UX
5. **Test on different screen sizes** for responsiveness
6. **Follow the established naming conventions**

## File Structure

```
integration-components/
├── index.ts                           # Main exports
├── modern-integration-wrapper.tsx     # Layout wrapper
├── modern-form-components.tsx         # Individual form components
├── integration-form-factory.tsx       # Factory component
└── README.md                          # This documentation
```
