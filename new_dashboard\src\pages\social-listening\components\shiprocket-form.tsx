import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToShipRocket } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/Shiprocket-logo.png';
import { shiprocketIntegrationSteps } from '../utils/constant';
import endPoints from '../apis/agent';
import { ModernIntegrationWrapper } from './integration-components/modern-integration-wrapper';
import { ModernSellerPanel } from './integration-components/modern-form-components';

interface FormFields {
   channelName: string;
   email: string;
   password: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const ShipRocketForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: 'shiprocket',
      email: '',
      password: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const {
      mutateAsync: validateShipRocketAcc,
      //isPending: isValidating,
      // errorMessage: validateShipRocketAccError,
   } = useApiMutation<
      { valid: boolean; message: string },
      { email: string; password: string }
   >({
      mutationFn: endPoints.validateShipRocketAcc,
   });

   const { mutateAsync: encryptCreds } = useApiMutation({
      mutationFn: endPoints.encryptCreds,
   });
   const handleConnect = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { email, password } = formFields;
         void (async () => {
            try {
               setTrying(true);
               setApiError({
                  success: false,
                  message: '',
               });
               const res = await validateShipRocketAcc({
                  email,
                  password,
               });
               const encryptedPassword = await encryptCreds({
                  password,
               });
               if (res.valid) {
                  console.log(res.message);
               }

               await connectDisconnectToShipRocket({
                  channel_name: 'shiprocket',
                  client_id,
                  email: email,
                  password: encryptedPassword,
                  isConnect: true,
               });
               setFormFields(defaultState);
               setApiError({
                  success: true,
                  message: 'Connection Established, Redirecting...',
               });
               setTimeout(() => {
                  navigate('/integrations');
               }, 3000);
            } catch (err) {
               let errMessage = 'Error connecting to shiprocket';
               if (
                  err &&
                  typeof err === 'object' &&
                  'response' in err &&
                  err.response &&
                  typeof err.response === 'object' &&
                  'data' in err.response &&
                  err.response.data &&
                  typeof err.response.data === 'object' &&
                  'message' in err.response.data
               ) {
                  errMessage =
                     (err.response.data as { message?: string }).message ||
                     errMessage;
               }
               setApiError({
                  success: false,
                  message: errMessage,
               });
            } finally {
               setTrying(false);
            }
         })();
      },
      [formFields, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   return (
      <ModernIntegrationWrapper
         title="Shiprocket Integration"
         description="Connect your Shiprocket account to manage shipments and logistics seamlessly"
         logo={image}
         logoAlt="Shiprocket Logo"
         steps={shiprocketIntegrationSteps}
      >
         <ModernSellerPanel
            title="Account Credentials"
            description="Please provide your Shiprocket API credentials to establish the connection"
            email={formFields.email}
            password={formFields.password}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText="Connect to Shiprocket"
            loadingText="Connecting to Shiprocket..."
         />
      </ModernIntegrationWrapper>
   );
};

export default ShipRocketForm;
